import { Request, Response } from "express";
import { StatusCodes } from "http-status-codes";
import analyticsService from "../services/analytics.service";
import { sequelize } from "../models/index";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "../helper/transaction.helper";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "../helper/validation.helper";
import analyticsController from "./analytics.controller";
import * as ExcelJS from "exceljs";
import { getOrgName } from "../helper/common";

/**
 * Get dashboard overview statistics - ROBUST VERSION
 * @route GET /api/v1/private/dashboard/overview
 * @access Private (Authenticated users)
 */
const getDashboardOverview = async (req: any, res: Response): Promise<any> => {
  try {
    // Essential user logging for dashboard access
    console.log("Dashboard Overview - Request user:", req.user ? {
      id: req.user.id,
      email: req.user.user_email,
      organizationId: req.user.organization_id,
      roles: req.user.roles?.map((r: any) => r.role_name)
    } : "No user object");

    // Check if user is authenticated
    if (!req.user || !req.user.id) {
      console.error("Dashboard Overview Error: User not authenticated");
      return res.status(401).json({
        status: false,
        message: "Authentication required"
      });
    }

    // Input validation and sanitization
    const sanitizedQuery = ValidationHelper.sanitizeInput(req.query);
    const { date_range = "last_30_days" } = sanitizedQuery;

    // Get effective organization ID (handles admin users)
    const effectiveOrganizationId =
      await ValidationHelper.getEffectiveOrganizationId(
        req.user,
        sanitizedQuery.organization_id
      );



    // Validate date_range parameter
    const validDateRanges = [
      "last_7_days",
      "last_30_days",
      "last_90_days",
      "last_year",
      "current_year",
      "custom",
    ];
    if (!validDateRanges.includes(date_range)) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message:
          "Invalid date_range. Must be one of: " + validDateRanges.join(", "),
      });
    }

    // Add timeout to prevent hanging
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error("Analytics service timeout")), 10000); // 10 second timeout
    });

    // Get dashboard stats from analytics service with timeout
    const stats: any = await Promise.race([
      analyticsService.getDashboardStats(
        effectiveOrganizationId,
        date_range as string
      ),
      timeoutPromise,
    ]);

    // Ensure consistent response structure with default values when no data exists
    const overviewData = {
      // Core Statistics - Dashboard Cards (matching your UI design)
      stats: {
        totalRecipes: stats.totalRecipes || 0,
        topCategory: stats.topCategory || { name: "No Data", count: 0 },
        highestImpressionRecipe: stats.highestImpressionRecipe || { name: "No Data", impressions: 0 },
      },

      // Analytics Statistics
      analytics: {
        totalViews: stats.totalViews || 0,
        totalContactSubmissions: stats.totalContactSubmissions || 0,
        totalBookmarks: stats.totalBookmarks || 0,
      },

      // Dashboard Charts Data (matching your UI design) - Always return arrays/objects even when empty
      charts: {
        recipeViewsTrend: Array.isArray(stats.recipeViewsTrend) ? stats.recipeViewsTrend : [], // Line chart - Top 10 recipes with single names
        categoryPerformance: Array.isArray(stats.categoryPerformance) ? stats.categoryPerformance : [], // Bar chart
        userEngagementHeatmap: Array.isArray(stats.userEngagementHeatmap) ? stats.userEngagementHeatmap : [], // Heatmap - NEW!
        conversionFunnel: Array.isArray(stats.conversionFunnel) ? stats.conversionFunnel : [], // Conversion analytics - NEW!
      },

      // Recent Activity - Always return array even when empty
      recentActivity: Array.isArray(stats.recentActivity) ? stats.recentActivity : [],
    };

    return res.status(StatusCodes.OK).json({
      status: true,
      message: "Dashboard overview fetched successfully",
      data: overviewData,
      timestamp: new Date().toISOString(),
    });
  } catch (error: any) {
    console.error("Dashboard Overview Error:", {
      message: error.message,
      userId: req.user?.id,
      organizationId: req.user?.organization_id,
      url: req.originalUrl
    });

    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: "Failed to fetch dashboard overview",
      error:
        process.env.NODE_ENV === "development"
          ? error.message
          : "Internal server error",
      timestamp: new Date().toISOString(),
    });
  }
};

/**
 * Get system health status
 * @route GET /api/v1/private/dashboard/health
 */
const getSystemHealth = async (_req: Request, res: Response): Promise<any> => {
  try {
    const healthChecks = {
      database: false,
      storage: false,
      analytics: false,
      overall: false,
    };

    // Check database connection
    try {
      await sequelize.authenticate();
      healthChecks.database = true;
    } catch (error) {
      console.error("Database health check failed:", error);
    }

    // Check storage (basic check)
    try {
      // This would check your file storage system
      healthChecks.storage = true;
    } catch (error) {
      console.error("Storage health check failed:", error);
    }

    // Check analytics
    try {
      // Use default organization for health check
      await analyticsService.getDashboardStats("default");
      healthChecks.analytics = true;
    } catch (error) {
      console.error("Analytics health check failed:", error);
    }

    // Overall health
    healthChecks.overall =
      healthChecks.database && healthChecks.storage && healthChecks.analytics;

    const status = healthChecks.overall
      ? StatusCodes.OK
      : StatusCodes.SERVICE_UNAVAILABLE;

    return res.status(status).json({
      status: healthChecks.overall,
      message: healthChecks.overall ? "System is healthy" : "System has issues",
      data: {
        checks: healthChecks,
        timestamp: new Date().toISOString(),
      },
    });
  } catch (error: any) {
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: "Health check failed",
      error: process.env.NODE_ENV === "development" ? error.message : undefined,
    });
  }
};

/**
 * Export dashboard data - ENHANCED VERSION WITH TIMEOUT AND BETTER ERROR HANDLING
 * @route GET /api/v1/private/dashboard/export
 */
const exportDashboardData = async (req: any, res: Response): Promise<any> => {
  try {
    // Input validation and sanitization
    const sanitizedQuery = ValidationHelper.sanitizeInput(req.query);
    const { format = "excel", date_range = "last_30_days" } = sanitizedQuery;

    // Get effective organization ID (handles admin users)
    const effectiveOrganizationId =
      await ValidationHelper.getEffectiveOrganizationId(
        req.user,
        sanitizedQuery.organization_id
      );

    // Add timeout to prevent hanging (same as dashboard overview)
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error("Export service timeout")), 15000); // 15 second timeout for export
    });

    // Get dashboard stats with timeout protection
    const dashboardData = await Promise.race([
      analyticsService.getDashboardStats(
        effectiveOrganizationId,
        date_range as string
      ),
      timeoutPromise,
    ]);

    if (format === "csv") {
      // Convert to CSV format with corrected data mapping
      const csv = convertToCSV(dashboardData, date_range);
      res.setHeader("Content-Type", "text/csv");
      res.setHeader(
        "Content-Disposition",
        `attachment; filename=dashboard-export-${new Date().toISOString().split("T")[0]}.csv`
      );
      return res.send(csv);
    }

    // Default Excel export
    return await exportDashboardToExcel(
      dashboardData,
      date_range,
      effectiveOrganizationId,
      res
    );
  } catch (error: any) {
    console.error("Dashboard export error:", error);

    // Handle specific error types
    if (error.message === "Export service timeout") {
      return res.status(StatusCodes.REQUEST_TIMEOUT).json({
        status: false,
        message:
          "Export request timed out. Please try again or contact support.",
        errorType: "TIMEOUT_ERROR",
      });
    }

    return ErrorHandler.createErrorResponse(
      error,
      res,
      "Error exporting dashboard data"
    );
  }
};

// Helper function to convert data to CSV - CORRECTED VERSION
function convertToCSV(data: any, dateRange: string = "last_30_days"): string {
  const headers = ["Metric", "Value"];

  // Use the actual data structure returned by getDashboardStats
  const rows = [
    // Export metadata
    ["Export Date", new Date().toISOString()],
    ["Date Range", dateRange],
    ["", ""], // Empty row for separation

    // Core Business Metrics
    ["Total Recipes", data.totalRecipes || 0],
    ["Top Category Name", data.topCategory?.name || "No Data"],
    ["Top Category Count", data.topCategory?.count || 0],
    ["", ""], // Empty row for separation

    // Recipe Analytics
    ["Total Views", data.totalViews || 0],
    ["Total Bookmarks", data.totalBookmarks || 0],
    ["Total Shares", data.totalShares || 0],
    ["Total Contact Submissions", data.totalContactSubmissions || 0],
    ["", ""], // Empty row for separation

    // Chart Data Summary
    ["Recipe Views Trend Data Points", data.recipeViewsTrend?.length || 0],
    ["Category Performance Data Points", data.categoryPerformance?.length || 0],
    [
      "User Engagement Heatmap Data Points",
      data.userEngagementHeatmap?.length || 0,
    ],
    ["Conversion Funnel Data Points", data.conversionFunnel?.length || 0],
    ["Recent Activity Items", data.recentActivity?.length || 0],
  ];

  const csvContent = [
    `# Dashboard Export - ${new Date().toISOString()}`,
    `# Date Range: ${dateRange}`,
    `# Generated by Recipe Management System`,
    "",
    headers.join(","),
    ...rows.map((row) => row.join(",")),
  ].join("\n");

  return csvContent;
}

/**
 * Export dashboard data to Excel format
 */
const exportDashboardToExcel = async (
  data: any,
  dateRange: string,
  organizationId: string | null | undefined,
  res: Response
): Promise<any> => {
  try {
    // Create Excel workbook
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet("Dashboard Export");

    // Get organization name
    const orgName = organizationId
      ? await getOrgName(organizationId)
      : "Organization";

    // Add organization name at the top with styling
    const orgRow = worksheet.addRow([orgName || "Organization"]);
    orgRow.font = { bold: true, size: 16, color: { argb: "FF135e96" } };
    orgRow.alignment = { horizontal: "center" };
    worksheet.mergeCells(1, 1, 1, 2); // Merge across 2 columns

    // Add empty row for spacing
    worksheet.addRow([]);

    // Add export information with better formatting
    const dateRow = worksheet.addRow([
      `Export Date: ${new Date().toLocaleString()}`,
    ]);
    dateRow.font = { bold: true, size: 12 };

    const rangeRow = worksheet.addRow([`Date Range: ${dateRange}`]);
    rangeRow.font = { bold: true, size: 12 };

    worksheet.addRow([]); // Empty row

    // Add headers with improved styling
    const headers = ["Metric", "Value"];
    const headerRow = worksheet.addRow(headers);
    headerRow.font = { bold: true, color: { argb: "FFFFFFFF" }, size: 12 };
    headerRow.fill = {
      type: "pattern",
      pattern: "solid",
      fgColor: { argb: "FF135e96" },
    };
    headerRow.alignment = { horizontal: "center", vertical: "middle" };
    headerRow.height = 25;

    // Add data rows
    const rows = [
      // Core Business Metrics
      ["Total Recipes", data.totalRecipes || 0],
      ["Active Users", data.activeUsers || 0],
      ["Top Category Name", data.topCategory?.name || "No Data"],
      ["Top Category Count", data.topCategory?.count || 0],
      [
        "Highest Impression Recipe",
        data.highestImpressionRecipe?.name || "No Data",
      ],
      [
        "Highest Recipe Impressions",
        data.highestImpressionRecipe?.impressions || 0,
      ],
      ["", ""], // Empty row for separation

      // Recipe Analytics
      ["Total Views", data.totalViews || 0],
      ["Total Contact Submissions", data.totalContactSubmissions || 0],
      ["", ""], // Empty row for separation

      // Chart Data Summary
      ["Recipe Views Trend Data Points", data.recipeViewsTrend?.length || 0],
      [
        "Category Performance Data Points",
        data.categoryPerformance?.length || 0,
      ],
      [
        "User Engagement Heatmap Data Points",
        data.userEngagementHeatmap?.length || 0,
      ],
      ["Conversion Funnel Data Points", data.conversionFunnel?.length || 0],
      ["Recent Activity Items", data.recentActivity?.length || 0],
    ];

    rows.forEach((row, index) => {
      const dataRow = worksheet.addRow(row);

      // Add alternating row colors for better readability (skip empty rows)
      if (row[0] !== "" && index % 2 === 0) {
        dataRow.fill = {
          type: "pattern",
          pattern: "solid",
          fgColor: { argb: "FFF8F9FA" }, // Light gray for even rows
        };
      }
    });

    // Auto-fit columns
    worksheet.columns.forEach((column) => {
      let maxLength = 0;

      if (column && column.eachCell) {
        column.eachCell({ includeEmpty: false }, (cell) => {
          const cellValue = cell.value ? cell.value.toString() : "";
          maxLength = Math.max(maxLength, cellValue.length);
        });
      }

      const calculatedWidth = Math.min(Math.max(maxLength + 3, 15), 50);
      if (column) {
        column.width = calculatedWidth;
      }
    });

    // Add freeze panes to keep headers visible
    worksheet.views = [
      {
        state: "frozen",
        xSplit: 0,
        ySplit: 6, // Freeze at row 6 (header row)
        topLeftCell: "A7",
        activeCell: "A7",
      },
    ];

    // Add print settings
    worksheet.pageSetup = {
      paperSize: 9, // A4
      orientation: "portrait",
      fitToPage: true,
      fitToWidth: 1,
      fitToHeight: 0,
      margins: {
        left: 0.7,
        right: 0.7,
        top: 0.75,
        bottom: 0.75,
        header: 0.3,
        footer: 0.3,
      },
    };

    // Add header and footer for printing
    worksheet.headerFooter.oddHeader = `&C&"Arial,Bold"&14${orgName || "Organization"} - Dashboard Export`;
    worksheet.headerFooter.oddFooter = `&L&"Arial"&10Generated on: ${new Date().toLocaleString()}&R&"Arial"&10Page &P of &N`;

    // Set response headers
    const filename = `dashboard-export-${new Date().toISOString().split("T")[0]}.xlsx`;
    res.setHeader(
      "Content-Type",
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    );
    res.setHeader("Content-Disposition", `attachment; filename=${filename}`);

    await workbook.xlsx.write(res);
    res.end();
  } catch (error) {
    console.error("Excel export error:", error);
    throw error;
  }
};

/**
 * Get CTA analytics for dashboard - Wrapper for analytics controller
 * @route GET /api/v1/private/dashboard/cta-analytics
 */
const getCtaAnalytics = async (req: any, res: Response): Promise<any> => {
  // Reuse existing analytics controller logic
  return analyticsController.getCtaClickAnalytics(req, res);
};

/**
 * Get contact analytics for dashboard - Wrapper for analytics controller
 * @route GET /api/v1/private/dashboard/contact-analytics
 */
const getContactAnalytics = async (req: any, res: Response): Promise<any> => {
  // Reuse existing analytics controller logic
  return analyticsController.getContactSubmissionAnalytics(req, res);
};
// CTA and Contact Analytics moved to unified analytics endpoints:
// - /v1/private/analytics/cta-analytics
// - /v1/private/analytics/contact-analytics

export default {
  getDashboardOverview,
  getSystemHealth,
  exportDashboardData,
};
