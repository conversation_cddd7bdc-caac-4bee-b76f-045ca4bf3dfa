/**
 * Test script to verify getDashboardOverview API returns consistent structure
 * when no data exists for the "last_year" filter
 */

const analyticsService = require('./src/services/analytics.service.ts');

// Mock request object for testing
const mockReq = {
  user: {
    id: 'test-user-id',
    user_email: '<EMAIL>',
    organization_id: 'test-org-id',
    roles: [{ role_name: 'user' }]
  },
  query: {
    date_range: 'last_year'
  }
};

// Mock response object for testing
const mockRes = {
  status: function(code) {
    this.statusCode = code;
    return this;
  },
  json: function(data) {
    this.responseData = data;
    return this;
  }
};

async function testDashboardOverviewStructure() {
  console.log('Testing getDashboardOverview with last_year filter...\n');
  
  try {
    // Import the dashboard controller
    const dashboardController = require('./src/controller/dashboard.controller.ts');
    
    // Call the getDashboardOverview method
    await dashboardController.default.getDashboardOverview(mockReq, mockRes);
    
    const response = mockRes.responseData;
    
    console.log('Response Status:', mockRes.statusCode);
    console.log('Response Structure:');
    console.log(JSON.stringify(response, null, 2));
    
    // Verify the response structure
    if (response && response.data) {
      const data = response.data;
      
      console.log('\n=== STRUCTURE VERIFICATION ===');
      
      // Check stats section
      console.log('✓ Stats section exists:', !!data.stats);
      if (data.stats) {
        console.log('  - totalRecipes:', data.stats.totalRecipes);
        console.log('  - topCategory:', data.stats.topCategory);
        console.log('  - highestImpressionRecipe:', data.stats.highestImpressionRecipe);
      }
      
      // Check analytics section
      console.log('✓ Analytics section exists:', !!data.analytics);
      if (data.analytics) {
        console.log('  - totalViews:', data.analytics.totalViews);
        console.log('  - totalContactSubmissions:', data.analytics.totalContactSubmissions);
        console.log('  - totalBookmarks:', data.analytics.totalBookmarks);
      }
      
      // Check charts section
      console.log('✓ Charts section exists:', !!data.charts);
      if (data.charts) {
        console.log('  - recipeViewsTrend is array:', Array.isArray(data.charts.recipeViewsTrend), 'Length:', data.charts.recipeViewsTrend?.length || 0);
        console.log('  - categoryPerformance is array:', Array.isArray(data.charts.categoryPerformance), 'Length:', data.charts.categoryPerformance?.length || 0);
        console.log('  - userEngagementHeatmap is array:', Array.isArray(data.charts.userEngagementHeatmap), 'Length:', data.charts.userEngagementHeatmap?.length || 0);
        console.log('  - conversionFunnel is array:', Array.isArray(data.charts.conversionFunnel), 'Length:', data.charts.conversionFunnel?.length || 0);
      }
      
      // Check recentActivity section
      console.log('✓ RecentActivity is array:', Array.isArray(data.recentActivity), 'Length:', data.recentActivity?.length || 0);
      
      console.log('\n=== TEST RESULT ===');
      
      // Verify all required sections exist
      const hasAllSections = data.stats && data.analytics && data.charts && Array.isArray(data.recentActivity);
      
      // Verify all chart arrays exist
      const hasAllChartArrays = data.charts && 
        Array.isArray(data.charts.recipeViewsTrend) &&
        Array.isArray(data.charts.categoryPerformance) &&
        Array.isArray(data.charts.userEngagementHeatmap) &&
        Array.isArray(data.charts.conversionFunnel);
      
      if (hasAllSections && hasAllChartArrays) {
        console.log('✅ SUCCESS: API returns consistent structure with empty arrays/default values when no data exists for last_year');
      } else {
        console.log('❌ FAILURE: API response structure is inconsistent');
        console.log('Missing sections:', {
          stats: !data.stats,
          analytics: !data.analytics,
          charts: !data.charts,
          recentActivity: !Array.isArray(data.recentActivity)
        });
        if (data.charts) {
          console.log('Missing chart arrays:', {
            recipeViewsTrend: !Array.isArray(data.charts.recipeViewsTrend),
            categoryPerformance: !Array.isArray(data.charts.categoryPerformance),
            userEngagementHeatmap: !Array.isArray(data.charts.userEngagementHeatmap),
            conversionFunnel: !Array.isArray(data.charts.conversionFunnel)
          });
        }
      }
    } else {
      console.log('❌ FAILURE: No data in response');
    }
    
  } catch (error) {
    console.error('❌ ERROR during test:', error.message);
    console.error('Stack trace:', error.stack);
  }
}

// Run the test
testDashboardOverviewStructure();
